<?php
// Target URL
$url = "https://egp.praz.org.zw/egp-SW5kZXhlcy9pbmRleA==";

// Initialize cURL
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

// Optional: Set headers to mimic a browser
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
]);

$html = curl_exec($ch);
curl_close($ch);

// Load HTML into DOMDocument
$dom = new DOMDocument();
libxml_use_internal_errors(true); // Suppress warnings for malformed HTML
$dom->loadHTML($html);
libxml_clear_errors();

// Find the table
$xpath = new DOMXPath($dom);
$rows = $xpath->query("//table//tr");

// Extract data
$data = [];
foreach ($rows as $index => $row) {
    // Skip header row
    if ($index === 0) continue;

    $cols = $row->getElementsByTagName('td');
    if ($cols->length < 8) continue; // Ensure enough columns

    $data[] = [
        'Tender Reference Number'       => trim($cols->item(0)->textContent),
        'Tender Title'                  => trim($cols->item(1)->textContent),
        'Required Supplier Category Code' => trim($cols->item(2)->textContent),
        'Required Supplier Category Name' => trim($cols->item(3)->textContent),
        'Procuring Entity'              => trim($cols->item(4)->textContent),
        'Scope'                         => trim($cols->item(5)->textContent),
        'Publish Date'                  => trim($cols->item(6)->textContent),
        'Closing Date'                  => trim($cols->item(7)->textContent),
    ];
}

// Output results
echo "<pre>";
print_r($data);
echo "</pre>";
?>